events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Upstream servers for HTTP API
    upstream perplexica_http {
        least_conn;
        server perplexica-mcp:3002 max_fails=3 fail_timeout=30s;
        server perplexica-mcp-http:3002 max_fails=3 fail_timeout=30s;
    }

    # Upstream servers for SSE
    upstream perplexica_sse {
        server perplexica-mcp:3001 max_fails=3 fail_timeout=30s;
        server perplexica-mcp-sse:3001 max_fails=3 fail_timeout=30s;
    }

    server {
        listen 80;
        server_name localhost;

        # Health check endpoint
        location /health {
            proxy_pass http://perplexica_http/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # HTTP API endpoints
        location /api/ {
            proxy_pass http://perplexica_http/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts for long-running searches
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s;
        }

        # SSE endpoint
        location /sse {
            proxy_pass http://perplexica_sse/sse;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # SSE specific headers
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 24h;
            
            # CORS headers for SSE
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        }

        # WebSocket upgrade support (if needed for future enhancements)
        location /ws {
            proxy_pass http://perplexica_sse;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static files or documentation (if added later)
        location /docs {
            alias /usr/share/nginx/html;
            index index.html;
        }

        # Default location for any other requests
        location / {
            return 200 '{"service": "perplexica-mcp", "status": "running", "endpoints": ["/api/search", "/sse", "/health"]}';
            add_header Content-Type application/json;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            return 500 '{"error": "Internal server error", "service": "perplexica-mcp"}';
            add_header Content-Type application/json;
        }
        
        location = /404.html {
            return 404 '{"error": "Not found", "service": "perplexica-mcp"}';
            add_header Content-Type application/json;
        }
    }

    # Optional: HTTPS configuration (uncomment and configure as needed)
    # server {
    #     listen 443 ssl http2;
    #     server_name localhost;
    #     
    #     ssl_certificate /etc/ssl/certs/nginx-selfsigned.crt;
    #     ssl_certificate_key /etc/ssl/private/nginx-selfsigned.key;
    #     
    #     # SSL configuration
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     
    #     # Include the same location blocks as above
    # }
}